# 📋 Preventivi (Quotes) Module Documentation

## 🎯 Overview

The Preventivi module is a comprehensive quote management system integrated into the ProgrammArti management system. It allows users to create, manage, and track quotes with dynamic work items and ChatGPT integration for analysis.

## ✨ Features

### 🔧 Core Functionality
- **Complete CRUD Operations**: Create, Read, Update, Delete quotes
- **Dynamic Work Items**: Add/remove work items with descriptions and costs
- **Real-time Cost Calculation**: Automatic total calculation
- **Client-Project Integration**: Dynamic project loading based on selected client
- **Status Management**: Track quote status (Draft, Sent, Accepted, Rejected, Expired)
- **Search & Filtering**: Advanced search and filter capabilities
- **ChatGPT Integration**: Automatic analysis and suggestions for quotes

### 🎨 User Interface
- **Responsive Design**: Consistent with existing ProgrammArti UI
- **Data Tables**: Paginated listing with search and filters
- **Dynamic Forms**: Interactive form with AJAX functionality
- **Status Badges**: Visual status indicators
- **Action Buttons**: Intuitive CRUD operations

## 🚀 Getting Started

### Prerequisites
- Laravel application with Spatie Permissions package
- Existing Client and Project models
- OpenAI API key (for ChatGPT integration)

### Installation Steps

1. **Run Migration**
   ```bash
   php artisan migrate
   ```

2. **Seed Permissions**
   ```bash
   php artisan db:seed --class=RoleSeeder
   ```

3. **Add Sample Data** (Optional)
   ```bash
   php artisan db:seed --class=PreventivoSeeder
   ```

4. **Configure OpenAI API Key**
   Add to your `.env` file:
   ```env
   OPENAI_API_KEY=your_openai_api_key_here
   ```

## 📱 Usage Guide

### Accessing the Module
1. Login to the ProgrammArti system
2. Navigate to "Preventivi" in the sidebar menu
3. Requires "manage quotes" permission

### Creating a New Quote

1. **Click "Nuovo Preventivo"** from the index page
2. **Select Client**: Choose from existing clients
3. **Select Project** (Optional): Projects load dynamically based on client
4. **Add Job Description**: Describe the work to be performed
5. **Add Work Items**:
   - Initial row is provided
   - Click "+" to add more items
   - Each item has description and cost
   - Remove items with "-" button (except first row)
6. **Review Total**: Automatically calculated
7. **Submit**: Creates quote and sends to ChatGPT for analysis

### Managing Quotes

#### Viewing Quotes
- **Index Page**: Lists all quotes with pagination
- **Search**: By client name, project, or description
- **Filter**: By status or client
- **Sort**: By creation date (newest first)

#### Quote Details
- **Client Information**: Name, email, phone
- **Project Details**: Associated project (if any)
- **Work Items**: Detailed breakdown with costs
- **Total Amount**: Calculated sum
- **ChatGPT Analysis**: AI-generated insights and suggestions
- **Status Management**: Change quote status

#### Editing Quotes
- **Modify all fields**: Client, project, description, work items
- **Dynamic work items**: Add/remove as needed
- **Status updates**: Change quote status
- **Recalculation**: Total updates automatically

## 🔧 Technical Details

### Database Structure
```sql
CREATE TABLE preventivi (
    id BIGINT PRIMARY KEY,
    client_id BIGINT FOREIGN KEY,
    project_id BIGINT FOREIGN KEY (nullable),
    job_description TEXT,
    work_items JSON,
    total_amount DECIMAL(10,2),
    status ENUM('draft', 'sent', 'accepted', 'rejected', 'expired'),
    chatgpt_response LONGTEXT (nullable),
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

### API Endpoints
- **GET /preventivi**: List quotes
- **POST /preventivi**: Create quote
- **GET /preventivi/{id}**: Show quote
- **PUT /preventivi/{id}**: Update quote
- **DELETE /preventivi/{id}**: Delete quote
- **GET /api/clients/{id}/projects**: Get client projects (AJAX)

### Permissions
- **manage quotes**: Required for all preventivi operations
- Assigned to: Admin, Developer, Social Media Manager roles

## 🤖 ChatGPT Integration

### How It Works
1. **Automatic Trigger**: Runs after quote creation
2. **Data Sent**: Job description, work items, total amount
3. **Analysis**: ChatGPT provides insights and suggestions
4. **Storage**: Response saved to database
5. **Display**: Shown in quote details page

### Configuration
Set your OpenAI API key in `.env`:
```env
OPENAI_API_KEY=sk-your-key-here
```

### Error Handling
- **API Failures**: Logged but don't prevent quote creation
- **Timeout**: 30-second timeout for API calls
- **Fallback**: Quotes work without ChatGPT if API unavailable

## 🎨 UI Components

### Status Colors
- **Draft**: Gray badge
- **Sent**: Blue badge
- **Accepted**: Green badge
- **Rejected**: Red badge
- **Expired**: Yellow badge

### Form Features
- **Dynamic Rows**: JavaScript-powered work item management
- **Real-time Calculation**: Instant total updates
- **AJAX Loading**: Dynamic project dropdown
- **Validation**: Client-side and server-side validation

## 🔍 Troubleshooting

### Common Issues

1. **Menu Not Visible**
   - Check user has "manage quotes" permission
   - Verify role assignments

2. **Projects Not Loading**
   - Check AJAX endpoint is accessible
   - Verify client has associated projects

3. **ChatGPT Not Working**
   - Verify OpenAI API key is set
   - Check internet connectivity
   - Review Laravel logs for errors

4. **Permission Errors**
   - Run: `php artisan db:seed --class=RoleSeeder`
   - Assign roles to users

### Debug Commands
```bash
# Check routes
php artisan route:list --name=preventivi

# Check permissions
php artisan tinker
>>> User::find(1)->can('manage quotes')

# Check model
php artisan tinker
>>> \App\Models\Preventivo::count()
```

## 📊 Sample Data

The module includes sample quotes demonstrating:
- **Web Development Quote**: Multi-phase project
- **Marketing Campaign**: Social media and advertising
- **Mobile App**: iOS/Android development
- **Consulting**: Performance optimization

## 🔄 Future Enhancements

Potential improvements:
- **PDF Export**: Generate PDF quotes
- **Email Integration**: Send quotes via email
- **Quote Templates**: Predefined work item templates
- **Approval Workflow**: Multi-step approval process
- **Quote Versioning**: Track quote revisions
- **Analytics Dashboard**: Quote statistics and insights

## 📞 Support

For technical support or feature requests, contact the development team or refer to the main ProgrammArti documentation.
