<?php $__env->startSection('page-title', 'Dettagli Preventivo'); ?>

<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        Dettagli Preventivo #<?php echo e($preventivo->id); ?>

     <?php $__env->endSlot(); ?>

    <div class="max-w-4xl mx-auto space-y-6">
        <!-- Header Actions -->
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Preventivo #<?php echo e($preventivo->id); ?></h1>
                <p class="text-gray-600">Creato il <?php echo e($preventivo->created_at->format('d/m/Y H:i')); ?></p>
            </div>
            <div class="flex space-x-3">
                <a href="<?php echo e(route('preventivi.edit', $preventivo)); ?>"
                   class="px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors duration-200">
                    <i class="fas fa-edit mr-2"></i>
                    Modifica
                </a>
                <a href="<?php echo e(route('preventivi.index')); ?>"
                   class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors duration-200">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Torna alla Lista
                </a>
            </div>
        </div>

        <!-- Main Content -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Left Column - Main Details -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Client and Project Info -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Informazioni Cliente e Progetto</h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Client Info -->
                        <div>
                            <h4 class="text-sm font-medium text-gray-700 mb-2">Cliente</h4>
                            <div class="space-y-1">
                                <p class="text-lg font-medium text-gray-900"><?php echo e($preventivo->client->full_name); ?></p>
                                <p class="text-sm text-gray-600"><?php echo e($preventivo->client->email); ?></p>
                                <?php if($preventivo->client->phone): ?>
                                    <p class="text-sm text-gray-600"><?php echo e($preventivo->client->phone); ?></p>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Project Info -->
                        <div>
                            <h4 class="text-sm font-medium text-gray-700 mb-2">Progetto</h4>
                            <?php if($preventivo->project): ?>
                                <div class="space-y-1">
                                    <p class="text-lg font-medium text-gray-900"><?php echo e($preventivo->project->name); ?></p>
                                    <p class="text-sm text-gray-600"><?php echo e($preventivo->project->project_type); ?></p>
                                </div>
                            <?php else: ?>
                                <p class="text-gray-500 italic">Nessun progetto associato</p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Job Description -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Descrizione del Lavoro</h3>
                    <div class="prose max-w-none">
                        <p class="text-gray-700 whitespace-pre-line"><?php echo e($preventivo->job_description); ?></p>
                    </div>
                </div>

                <!-- Work Items -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Voci di Lavoro</h3>
                    
                    <div class="space-y-3">
                        <?php $__currentLoopData = $preventivo->work_items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                                <div class="flex-1">
                                    <p class="font-medium text-gray-900"><?php echo e($item['description']); ?></p>
                                </div>
                                <div class="text-right">
                                    <p class="text-lg font-semibold text-gray-900">€<?php echo e(number_format($item['cost'], 2, ',', '.')); ?></p>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>

                    <!-- Total -->
                    <div class="mt-4 pt-4 border-t border-gray-200">
                        <div class="flex justify-between items-center">
                            <span class="text-xl font-semibold text-gray-900">Totale:</span>
                            <span class="text-2xl font-bold text-blue-600">€<?php echo e(number_format($preventivo->total_amount, 2, ',', '.')); ?></span>
                        </div>
                    </div>
                </div>

                <!-- ChatGPT Analysis -->
                <?php if($preventivo->chatgpt_response): ?>
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">
                            <i class="fas fa-robot mr-2 text-blue-600"></i>
                            Analisi ChatGPT
                        </h3>
                        <div class="prose max-w-none">
                            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                <p class="text-gray-700 whitespace-pre-line"><?php echo e($preventivo->chatgpt_response); ?></p>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Right Column - Status and Actions -->
            <div class="space-y-6">
                <!-- Status Card -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Stato Preventivo</h3>
                    
                    <div class="space-y-4">
                        <div>
                            <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full <?php echo e($preventivo->status_color); ?>">
                                <?php echo e($preventivo->status_label); ?>

                            </span>
                        </div>

                        <!-- Status Change Form -->
                        <form action="<?php echo e(route('preventivi.update', $preventivo)); ?>" method="POST">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('PATCH'); ?>
                            
                            <!-- Hidden fields to maintain current data -->
                            <input type="hidden" name="client_id" value="<?php echo e($preventivo->client_id); ?>">
                            <input type="hidden" name="project_id" value="<?php echo e($preventivo->project_id); ?>">
                            <input type="hidden" name="job_description" value="<?php echo e($preventivo->job_description); ?>">
                            <?php $__currentLoopData = $preventivo->work_items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <input type="hidden" name="work_items[<?php echo e($index); ?>][description]" value="<?php echo e($item['description']); ?>">
                                <input type="hidden" name="work_items[<?php echo e($index); ?>][cost]" value="<?php echo e($item['cost']); ?>">
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            
                            <div>
                                <label for="status" class="block text-sm font-medium text-gray-700 mb-2">
                                    Cambia Stato
                                </label>
                                <select name="status" 
                                        id="status"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                    <?php $__currentLoopData = \App\Models\Preventivo::getStatuses(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($value); ?>" <?php echo e($preventivo->status === $value ? 'selected' : ''); ?>>
                                            <?php echo e($label); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                            
                            <button type="submit"
                                    class="w-full mt-3 px-4 py-2 text-white rounded-lg transition-colors duration-200"
                                    style="background-color: #007BCE;"
                                    onmouseover="this.style.backgroundColor='#005B99'"
                                    onmouseout="this.style.backgroundColor='#007BCE'">
                                Aggiorna Stato
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Quick Stats -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Statistiche</h3>
                    
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Numero voci:</span>
                            <span class="text-sm font-medium text-gray-900"><?php echo e(count($preventivo->work_items)); ?></span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Costo medio per voce:</span>
                            <span class="text-sm font-medium text-gray-900">
                                €<?php echo e(number_format($preventivo->total_amount / count($preventivo->work_items), 2, ',', '.')); ?>

                            </span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Ultima modifica:</span>
                            <span class="text-sm font-medium text-gray-900"><?php echo e($preventivo->updated_at->format('d/m/Y H:i')); ?></span>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Azioni</h3>
                    
                    <div class="space-y-3">
                        <a href="<?php echo e(route('preventivi.edit', $preventivo)); ?>"
                           class="w-full inline-flex justify-center items-center px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors duration-200">
                            <i class="fas fa-edit mr-2"></i>
                            Modifica Preventivo
                        </a>
                        
                        <form action="<?php echo e(route('preventivi.destroy', $preventivo)); ?>" 
                              method="POST" 
                              onsubmit="return confirm('Sei sicuro di voler eliminare questo preventivo?')">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('DELETE'); ?>
                            <button type="submit"
                                    class="w-full inline-flex justify-center items-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors duration-200">
                                <i class="fas fa-trash mr-2"></i>
                                Elimina Preventivo
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Desktop\Programmarti Gestionale\programmarti-gest\resources\views/preventivi/show.blade.php ENDPATH**/ ?>