@section('page-title', 'Modifica Preventivo')

<x-app-layout>
    <x-slot name="header">
        Modifica Preventivo #{{ $preventivo->id }}
    </x-slot>

    <div class="max-w-4xl mx-auto">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-xl font-semibold text-gray-900">Modifica Preventivo</h2>
            </div>

            <form action="{{ route('preventivi.update', $preventivo) }}" method="POST" class="p-6 space-y-6" id="preventivoForm">
                @csrf
                @method('PATCH')

                <!-- Client Selection -->
                <div>
                    <label for="client_id" class="block text-sm font-medium text-gray-700 mb-2">
                        Cliente *
                    </label>
                    <select id="client_id" 
                            name="client_id" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('client_id') border-red-500 @enderror"
                            required
                            onchange="loadClientProjects()">
                        <option value="">Seleziona cliente</option>
                        @foreach($clients as $client)
                            <option value="{{ $client->id }}" {{ old('client_id', $preventivo->client_id) == $client->id ? 'selected' : '' }}>
                                {{ $client->full_name }} ({{ $client->email }})
                            </option>
                        @endforeach
                    </select>
                    @error('client_id')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Project Selection (Dynamic) -->
                <div>
                    <label for="project_id" class="block text-sm font-medium text-gray-700 mb-2">
                        Progetto (Opzionale)
                    </label>
                    <select id="project_id" 
                            name="project_id" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('project_id') border-red-500 @enderror">
                        <option value="">Nessun progetto</option>
                        @foreach($projects as $project)
                            <option value="{{ $project->id }}" {{ old('project_id', $preventivo->project_id) == $project->id ? 'selected' : '' }}>
                                {{ $project->name }}
                            </option>
                        @endforeach
                    </select>
                    @error('project_id')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Job Description -->
                <div>
                    <label for="job_description" class="block text-sm font-medium text-gray-700 mb-2">
                        Descrizione del Lavoro *
                    </label>
                    <textarea id="job_description" 
                              name="job_description" 
                              rows="4"
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('job_description') border-red-500 @enderror"
                              placeholder="Descrivi il lavoro da svolgere..."
                              required>{{ old('job_description', $preventivo->job_description) }}</textarea>
                    @error('job_description')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Status -->
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-2">
                        Stato *
                    </label>
                    <select id="status" 
                            name="status" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('status') border-red-500 @enderror"
                            required>
                        @foreach(\App\Models\Preventivo::getStatuses() as $value => $label)
                            <option value="{{ $value }}" {{ old('status', $preventivo->status) === $value ? 'selected' : '' }}>
                                {{ $label }}
                            </option>
                        @endforeach
                    </select>
                    @error('status')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Dynamic Work Items Section -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-4">
                        Voci di Lavoro *
                    </label>
                    
                    <div id="workItemsContainer" class="space-y-3">
                        @foreach(old('work_items', $preventivo->work_items) as $index => $item)
                            <div class="work-item-row flex gap-3 items-end">
                                <div class="flex-1">
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Descrizione</label>
                                    <input type="text" 
                                           name="work_items[{{ $index }}][description]" 
                                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                           placeholder="Descrizione del lavoro..."
                                           value="{{ $item['description'] }}"
                                           required>
                                </div>
                                <div class="w-32">
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Costo (€)</label>
                                    <input type="number" 
                                           name="work_items[{{ $index }}][cost]" 
                                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 work-item-cost"
                                           placeholder="0.00"
                                           step="0.01"
                                           min="0"
                                           value="{{ $item['cost'] }}"
                                           required
                                           onchange="calculateTotal()">
                                </div>
                                <div class="w-10">
                                    @if($index > 0)
                                        <button type="button" 
                                                onclick="removeWorkItem(this)"
                                                class="p-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg transition-colors duration-200">
                                            <i class="fas fa-minus"></i>
                                        </button>
                                    @endif
                                </div>
                            </div>
                        @endforeach
                    </div>

                    <!-- Add Work Item Button -->
                    <div class="mt-4">
                        <button type="button" 
                                onclick="addWorkItem()"
                                class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200">
                            <i class="fas fa-plus mr-2"></i>
                            Aggiungi Voce
                        </button>
                    </div>

                    <!-- Total Display -->
                    <div class="mt-4 p-4 bg-gray-50 rounded-lg">
                        <div class="flex justify-between items-center">
                            <span class="text-lg font-medium text-gray-900">Totale:</span>
                            <span id="totalAmount" class="text-xl font-bold text-blue-600">€{{ number_format($preventivo->total_amount, 2, ',', '.') }}</span>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                    <a href="{{ route('preventivi.show', $preventivo) }}" 
                       class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors duration-200">
                        Annulla
                    </a>
                    <button type="submit"
                            class="px-4 py-2 text-white rounded-lg transition-colors duration-200"
                            style="background-color: #007BCE;"
                            onmouseover="this.style.backgroundColor='#005B99'"
                            onmouseout="this.style.backgroundColor='#007BCE'">
                        Aggiorna Preventivo
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        let workItemIndex = {{ count($preventivo->work_items) }};

        // Load projects when client is selected
        async function loadClientProjects() {
            const clientId = document.getElementById('client_id').value;
            const projectSelect = document.getElementById('project_id');
            const currentProjectId = {{ $preventivo->project_id ?? 'null' }};
            
            if (!clientId) {
                projectSelect.innerHTML = '<option value="">Seleziona prima un cliente</option>';
                projectSelect.disabled = true;
                return;
            }

            try {
                const response = await fetch(`/api/clients/${clientId}/projects`);
                const data = await response.json();
                
                if (data.success) {
                    projectSelect.innerHTML = '<option value="">Nessun progetto</option>';
                    data.projects.forEach(project => {
                        const selected = project.id === currentProjectId ? 'selected' : '';
                        projectSelect.innerHTML += `<option value="${project.id}" ${selected}>${project.name}</option>`;
                    });
                    projectSelect.disabled = false;
                } else {
                    console.error('Error loading projects:', data);
                }
            } catch (error) {
                console.error('Error loading projects:', error);
                projectSelect.innerHTML = '<option value="">Errore nel caricamento</option>';
            }
        }

        // Add new work item row
        function addWorkItem() {
            const container = document.getElementById('workItemsContainer');
            const newRow = document.createElement('div');
            newRow.className = 'work-item-row flex gap-3 items-end';
            newRow.innerHTML = `
                <div class="flex-1">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Descrizione</label>
                    <input type="text" 
                           name="work_items[${workItemIndex}][description]" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                           placeholder="Descrizione del lavoro..."
                           required>
                </div>
                <div class="w-32">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Costo (€)</label>
                    <input type="number" 
                           name="work_items[${workItemIndex}][cost]" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 work-item-cost"
                           placeholder="0.00"
                           step="0.01"
                           min="0"
                           required
                           onchange="calculateTotal()">
                </div>
                <div class="w-10">
                    <button type="button" 
                            onclick="removeWorkItem(this)"
                            class="p-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg transition-colors duration-200">
                        <i class="fas fa-minus"></i>
                    </button>
                </div>
            `;
            container.appendChild(newRow);
            workItemIndex++;
        }

        // Remove work item row
        function removeWorkItem(button) {
            button.closest('.work-item-row').remove();
            calculateTotal();
        }

        // Calculate total amount
        function calculateTotal() {
            const costInputs = document.querySelectorAll('.work-item-cost');
            let total = 0;
            
            costInputs.forEach(input => {
                const value = parseFloat(input.value) || 0;
                total += value;
            });
            
            document.getElementById('totalAmount').textContent = `€${total.toFixed(2)}`;
        }

        // Initialize total calculation on page load
        document.addEventListener('DOMContentLoaded', function() {
            calculateTotal();
        });
    </script>
</x-app-layout>
