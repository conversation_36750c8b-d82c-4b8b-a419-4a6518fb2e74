<?php

namespace App\Http\Controllers;

use App\Models\Preventivo;
use App\Models\Client;
use App\Models\Project;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class PreventivoController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Preventivo::with(['client', 'project']);

        // Search functionality
        if ($request->has('search') && $request->search !== '') {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->whereHas('client', function ($clientQuery) use ($search) {
                    $clientQuery->where('first_name', 'like', "%{$search}%")
                               ->orWhere('last_name', 'like', "%{$search}%")
                               ->orWhere('email', 'like', "%{$search}%");
                })
                ->orWhereHas('project', function ($projectQuery) use ($search) {
                    $projectQuery->where('name', 'like', "%{$search}%");
                })
                ->orWhere('job_description', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->has('status') && $request->status !== '') {
            $query->where('status', $request->status);
        }

        // Filter by client
        if ($request->has('client_id') && $request->client_id !== '') {
            $query->where('client_id', $request->client_id);
        }

        $preventivi = $query->orderBy('created_at', 'desc')->paginate(15);
        $clients = Client::orderBy('first_name')->get();

        return view('preventivi.index', compact('preventivi', 'clients'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $clients = Client::orderBy('first_name')->get();
        return view('preventivi.create', compact('clients'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'client_id' => 'required|exists:clients,id',
            'project_id' => 'nullable|exists:projects,id',
            'job_description' => 'required|string',
            'work_items' => 'required|array|min:1',
            'work_items.*.description' => 'required|string',
            'work_items.*.cost' => 'required|numeric|min:0',
        ]);

        // Calculate total amount
        $totalAmount = collect($validated['work_items'])->sum('cost');
        $validated['total_amount'] = $totalAmount;
        $validated['status'] = 'draft';

        $preventivo = Preventivo::create($validated);

        // Send to ChatGPT API
        $this->sendToChatGPT($preventivo);

        return redirect()->route('preventivi.index')
                        ->with('success', 'Preventivo creato con successo.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Preventivo $preventivo)
    {
        $preventivo->load(['client', 'project']);
        return view('preventivi.show', compact('preventivo'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Preventivo $preventivo)
    {
        $clients = Client::orderBy('first_name')->get();
        $projects = $preventivo->client ? $preventivo->client->projects : collect();
        return view('preventivi.edit', compact('preventivo', 'clients', 'projects'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Preventivo $preventivo)
    {
        $validated = $request->validate([
            'client_id' => 'required|exists:clients,id',
            'project_id' => 'nullable|exists:projects,id',
            'job_description' => 'required|string',
            'work_items' => 'required|array|min:1',
            'work_items.*.description' => 'required|string',
            'work_items.*.cost' => 'required|numeric|min:0',
            'status' => 'required|in:draft,sent,accepted,rejected,expired',
        ]);

        // Calculate total amount
        $totalAmount = collect($validated['work_items'])->sum('cost');
        $validated['total_amount'] = $totalAmount;

        $preventivo->update($validated);

        return redirect()->route('preventivi.index')
                        ->with('success', 'Preventivo aggiornato con successo.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Preventivo $preventivo)
    {
        $preventivo->delete();

        return redirect()->route('preventivi.index')
                        ->with('success', 'Preventivo eliminato con successo.');
    }

    /**
     * Get projects for a specific client (AJAX endpoint)
     */
    public function getClientProjects(Client $client): JsonResponse
    {
        $projects = $client->projects()->select('id', 'name')->get();
        
        return response()->json([
            'success' => true,
            'projects' => $projects
        ]);
    }

    /**
     * Send preventivo data to ChatGPT API
     */
    private function sendToChatGPT(Preventivo $preventivo): void
    {
        try {
            // Prepare data for ChatGPT
            $workItemsText = collect($preventivo->work_items)
                ->map(fn($item) => "- {$item['description']}: €{$item['cost']}")
                ->join("\n");

            $prompt = "Analizza questo preventivo:\n\n" .
                     "Descrizione lavoro: {$preventivo->job_description}\n\n" .
                     "Voci di lavoro:\n{$workItemsText}\n\n" .
                     "Totale: €{$preventivo->total_amount}\n\n" .
                     "Fornisci un'analisi dettagliata e suggerimenti per migliorare il preventivo.";

            // Make API call to ChatGPT
            $response = Http::timeout(30)->post('https://api.openai.com/v1/chat/completions', [
                'model' => 'gpt-3.5-turbo',
                'messages' => [
                    [
                        'role' => 'system',
                        'content' => 'Sei un esperto consulente per preventivi e analisi di progetti. Fornisci analisi dettagliate e consigli pratici.'
                    ],
                    [
                        'role' => 'user',
                        'content' => $prompt
                    ]
                ],
                'max_tokens' => 1000,
                'temperature' => 0.7,
            ], [
                'Authorization' => 'Bearer ' . env('OPENAI_API_KEY'),
                'Content-Type' => 'application/json',
            ]);

            if ($response->successful()) {
                $data = $response->json();
                $chatgptResponse = $data['choices'][0]['message']['content'] ?? 'Nessuna risposta ricevuta';
                
                $preventivo->update(['chatgpt_response' => $chatgptResponse]);
            } else {
                Log::error('ChatGPT API Error', [
                    'status' => $response->status(),
                    'body' => $response->body()
                ]);
            }
        } catch (\Exception $e) {
            Log::error('ChatGPT Integration Error', [
                'message' => $e->getMessage(),
                'preventivo_id' => $preventivo->id
            ]);
        }
    }
}
