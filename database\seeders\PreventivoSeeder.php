<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Preventivo;
use App\Models\Client;
use App\Models\Project;

class PreventivoSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get some existing clients and projects
        $clients = Client::all();
        $projects = Project::all();

        if ($clients->isEmpty()) {
            $this->command->warn('No clients found. Please run ClientSeeder first.');
            return;
        }

        // Sample preventivi data
        $preventivi = [
            [
                'client_id' => $clients->first()->id,
                'project_id' => $projects->isNotEmpty() ? $projects->first()->id : null,
                'job_description' => 'Sviluppo di un sito web aziendale completo con sistema di gestione contenuti, ottimizzazione SEO e integrazione social media.',
                'work_items' => [
                    ['description' => 'Analisi e progettazione UX/UI', 'cost' => 800.00],
                    ['description' => 'Sviluppo frontend responsive', 'cost' => 1200.00],
                    ['description' => 'Sviluppo backend e CMS', 'cost' => 1500.00],
                    ['description' => 'Ottimizzazione SEO', 'cost' => 400.00],
                    ['description' => 'Testing e deployment', 'cost' => 300.00],
                ],
                'total_amount' => 4200.00,
                'status' => 'sent',
                'chatgpt_response' => 'Questo preventivo presenta una struttura ben bilanciata per lo sviluppo di un sito web aziendale. I costi sono ragionevoli e la suddivisione delle attività è chiara. Suggerisco di considerare l\'aggiunta di un pacchetto di manutenzione post-lancio.',
            ],
            [
                'client_id' => $clients->count() > 1 ? $clients->skip(1)->first()->id : $clients->first()->id,
                'project_id' => $projects->count() > 1 ? $projects->skip(1)->first()->id : null,
                'job_description' => 'Campagna di marketing digitale per il lancio di un nuovo prodotto, inclusa gestione social media e advertising.',
                'work_items' => [
                    ['description' => 'Strategia di marketing digitale', 'cost' => 600.00],
                    ['description' => 'Creazione contenuti social (1 mese)', 'cost' => 800.00],
                    ['description' => 'Gestione campagne Facebook/Instagram Ads', 'cost' => 500.00],
                    ['description' => 'Analisi e reportistica', 'cost' => 200.00],
                ],
                'total_amount' => 2100.00,
                'status' => 'draft',
                'chatgpt_response' => null,
            ],
            [
                'client_id' => $clients->count() > 2 ? $clients->skip(2)->first()->id : $clients->first()->id,
                'project_id' => null,
                'job_description' => 'Sviluppo di un\'applicazione mobile per la gestione degli ordini e comunicazione con i clienti.',
                'work_items' => [
                    ['description' => 'Progettazione UI/UX mobile', 'cost' => 1000.00],
                    ['description' => 'Sviluppo app iOS', 'cost' => 2500.00],
                    ['description' => 'Sviluppo app Android', 'cost' => 2500.00],
                    ['description' => 'Backend API e database', 'cost' => 1800.00],
                    ['description' => 'Testing e pubblicazione store', 'cost' => 400.00],
                ],
                'total_amount' => 8200.00,
                'status' => 'accepted',
                'chatgpt_response' => 'Preventivo molto dettagliato per lo sviluppo di un\'app mobile. I costi sono in linea con gli standard del mercato. Consiglio di includere un periodo di supporto post-lancio e di considerare l\'aggiunta di funzionalità di analytics.',
            ],
            [
                'client_id' => $clients->first()->id,
                'project_id' => null,
                'job_description' => 'Consulenza per l\'ottimizzazione delle performance del sito web esistente e miglioramento della velocità di caricamento.',
                'work_items' => [
                    ['description' => 'Audit tecnico completo', 'cost' => 300.00],
                    ['description' => 'Ottimizzazione immagini e media', 'cost' => 200.00],
                    ['description' => 'Ottimizzazione codice e database', 'cost' => 400.00],
                    ['description' => 'Implementazione CDN', 'cost' => 150.00],
                ],
                'total_amount' => 1050.00,
                'status' => 'rejected',
                'chatgpt_response' => 'Preventivo focalizzato sull\'ottimizzazione delle performance. I costi sono competitivi. Suggerisco di aggiungere un servizio di monitoraggio continuo delle performance per garantire risultati duraturi.',
            ],
        ];

        foreach ($preventivi as $preventivoData) {
            Preventivo::create($preventivoData);
        }

        $this->command->info('Created ' . count($preventivi) . ' sample preventivi.');
    }
}
