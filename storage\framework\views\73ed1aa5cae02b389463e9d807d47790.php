<?php $__env->startSection('page-title', 'Nuovo Preventivo'); ?>

<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        Nuovo Preventivo
     <?php $__env->endSlot(); ?>

    <div class="max-w-4xl mx-auto">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-xl font-semibold text-gray-900">Crea Nuovo Preventivo</h2>
            </div>

            <form action="<?php echo e(route('preventivi.store')); ?>" method="POST" class="p-6 space-y-6" id="preventivoForm">
                <?php echo csrf_field(); ?>

                <!-- Client Selection -->
                <div>
                    <label for="client_id" class="block text-sm font-medium text-gray-700 mb-2">
                        Cliente *
                    </label>
                    <select id="client_id" 
                            name="client_id" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 <?php $__errorArgs = ['client_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                            required
                            onchange="loadClientProjects()">
                        <option value="">Seleziona cliente</option>
                        <?php $__currentLoopData = $clients; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $client): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($client->id); ?>" <?php echo e(old('client_id') == $client->id ? 'selected' : ''); ?>>
                                <?php echo e($client->full_name); ?> (<?php echo e($client->email); ?>)
                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                    <?php $__errorArgs = ['client_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <!-- Project Selection (Dynamic) -->
                <div>
                    <label for="project_id" class="block text-sm font-medium text-gray-700 mb-2">
                        Progetto (Opzionale)
                    </label>
                    <select id="project_id" 
                            name="project_id" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 <?php $__errorArgs = ['project_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                            disabled>
                        <option value="">Seleziona prima un cliente</option>
                    </select>
                    <?php $__errorArgs = ['project_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <!-- Job Description -->
                <div>
                    <label for="job_description" class="block text-sm font-medium text-gray-700 mb-2">
                        Descrizione del Lavoro *
                    </label>
                    <textarea id="job_description" 
                              name="job_description" 
                              rows="4"
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 <?php $__errorArgs = ['job_description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                              placeholder="Descrivi il lavoro da svolgere..."
                              required><?php echo e(old('job_description')); ?></textarea>
                    <?php $__errorArgs = ['job_description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <!-- Dynamic Work Items Section -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-4">
                        Voci di Lavoro *
                    </label>
                    
                    <div id="workItemsContainer" class="space-y-3">
                        <!-- Initial work item row -->
                        <div class="work-item-row flex gap-3 items-end">
                            <div class="flex-1">
                                <label class="block text-sm font-medium text-gray-700 mb-1">Descrizione</label>
                                <input type="text" 
                                       name="work_items[0][description]" 
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                       placeholder="Descrizione del lavoro..."
                                       required>
                            </div>
                            <div class="w-32">
                                <label class="block text-sm font-medium text-gray-700 mb-1">Costo (€)</label>
                                <input type="number" 
                                       name="work_items[0][cost]" 
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 work-item-cost"
                                       placeholder="0.00"
                                       step="0.01"
                                       min="0"
                                       required
                                       onchange="calculateTotal()">
                            </div>
                            <div class="w-10">
                                <!-- First row doesn't have remove button -->
                            </div>
                        </div>
                    </div>

                    <!-- Add Work Item Button -->
                    <div class="mt-4">
                        <button type="button" 
                                onclick="addWorkItem()"
                                class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200">
                            <i class="fas fa-plus mr-2"></i>
                            Aggiungi Voce
                        </button>
                    </div>

                    <!-- Total Display -->
                    <div class="mt-4 p-4 bg-gray-50 rounded-lg">
                        <div class="flex justify-between items-center">
                            <span class="text-lg font-medium text-gray-900">Totale:</span>
                            <span id="totalAmount" class="text-xl font-bold text-blue-600">€0.00</span>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                    <a href="<?php echo e(route('preventivi.index')); ?>" 
                       class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors duration-200">
                        Annulla
                    </a>
                    <button type="submit"
                            class="px-4 py-2 text-white rounded-lg transition-colors duration-200"
                            style="background-color: #007BCE;"
                            onmouseover="this.style.backgroundColor='#005B99'"
                            onmouseout="this.style.backgroundColor='#007BCE'">
                        Crea Preventivo
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        let workItemIndex = 1;

        // Load projects when client is selected
        async function loadClientProjects() {
            const clientId = document.getElementById('client_id').value;
            const projectSelect = document.getElementById('project_id');
            
            if (!clientId) {
                projectSelect.innerHTML = '<option value="">Seleziona prima un cliente</option>';
                projectSelect.disabled = true;
                return;
            }

            try {
                const response = await fetch(`/api/clients/${clientId}/projects`);
                const data = await response.json();
                
                if (data.success) {
                    projectSelect.innerHTML = '<option value="">Nessun progetto</option>';
                    data.projects.forEach(project => {
                        projectSelect.innerHTML += `<option value="${project.id}">${project.name}</option>`;
                    });
                    projectSelect.disabled = false;
                } else {
                    console.error('Error loading projects:', data);
                }
            } catch (error) {
                console.error('Error loading projects:', error);
                projectSelect.innerHTML = '<option value="">Errore nel caricamento</option>';
            }
        }

        // Add new work item row
        function addWorkItem() {
            const container = document.getElementById('workItemsContainer');
            const newRow = document.createElement('div');
            newRow.className = 'work-item-row flex gap-3 items-end';
            newRow.innerHTML = `
                <div class="flex-1">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Descrizione</label>
                    <input type="text" 
                           name="work_items[${workItemIndex}][description]" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                           placeholder="Descrizione del lavoro..."
                           required>
                </div>
                <div class="w-32">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Costo (€)</label>
                    <input type="number" 
                           name="work_items[${workItemIndex}][cost]" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 work-item-cost"
                           placeholder="0.00"
                           step="0.01"
                           min="0"
                           required
                           onchange="calculateTotal()">
                </div>
                <div class="w-10">
                    <button type="button" 
                            onclick="removeWorkItem(this)"
                            class="p-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg transition-colors duration-200">
                        <i class="fas fa-minus"></i>
                    </button>
                </div>
            `;
            container.appendChild(newRow);
            workItemIndex++;
        }

        // Remove work item row
        function removeWorkItem(button) {
            button.closest('.work-item-row').remove();
            calculateTotal();
        }

        // Calculate total amount
        function calculateTotal() {
            const costInputs = document.querySelectorAll('.work-item-cost');
            let total = 0;
            
            costInputs.forEach(input => {
                const value = parseFloat(input.value) || 0;
                total += value;
            });
            
            document.getElementById('totalAmount').textContent = `€${total.toFixed(2)}`;
        }

        // Initialize total calculation on page load
        document.addEventListener('DOMContentLoaded', function() {
            calculateTotal();
        });
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Desktop\Programmarti Gestionale\programmarti-gest\resources\views/preventivi/create.blade.php ENDPATH**/ ?>