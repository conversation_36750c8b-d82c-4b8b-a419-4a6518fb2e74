@section('page-title', 'Dettagli Preventivo')

<x-app-layout>
    <x-slot name="header">
        Dettagli Preventivo #{{ $preventivo->id }}
    </x-slot>

    <div class="max-w-4xl mx-auto space-y-6">
        <!-- Header Actions -->
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Preventivo #{{ $preventivo->id }}</h1>
                <p class="text-gray-600">Creato il {{ $preventivo->created_at->format('d/m/Y H:i') }}</p>
            </div>
            <div class="flex space-x-3">
                <a href="{{ route('preventivi.edit', $preventivo) }}"
                   class="px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors duration-200">
                    <i class="fas fa-edit mr-2"></i>
                    Modifica
                </a>
                <a href="{{ route('preventivi.index') }}"
                   class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors duration-200">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Torna alla Lista
                </a>
            </div>
        </div>

        <!-- Main Content -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Left Column - Main Details -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Client and Project Info -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Informazioni Cliente e Progetto</h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Client Info -->
                        <div>
                            <h4 class="text-sm font-medium text-gray-700 mb-2">Cliente</h4>
                            <div class="space-y-1">
                                <p class="text-lg font-medium text-gray-900">{{ $preventivo->client->full_name }}</p>
                                <p class="text-sm text-gray-600">{{ $preventivo->client->email }}</p>
                                @if($preventivo->client->phone)
                                    <p class="text-sm text-gray-600">{{ $preventivo->client->phone }}</p>
                                @endif
                            </div>
                        </div>

                        <!-- Project Info -->
                        <div>
                            <h4 class="text-sm font-medium text-gray-700 mb-2">Progetto</h4>
                            @if($preventivo->project)
                                <div class="space-y-1">
                                    <p class="text-lg font-medium text-gray-900">{{ $preventivo->project->name }}</p>
                                    <p class="text-sm text-gray-600">{{ $preventivo->project->project_type }}</p>
                                </div>
                            @else
                                <p class="text-gray-500 italic">Nessun progetto associato</p>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Job Description -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Descrizione del Lavoro</h3>
                    <div class="prose max-w-none">
                        <p class="text-gray-700 whitespace-pre-line">{{ $preventivo->job_description }}</p>
                    </div>
                </div>

                <!-- Work Items -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Voci di Lavoro</h3>
                    
                    <div class="space-y-3">
                        @foreach($preventivo->work_items as $index => $item)
                            <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                                <div class="flex-1">
                                    <p class="font-medium text-gray-900">{{ $item['description'] }}</p>
                                </div>
                                <div class="text-right">
                                    <p class="text-lg font-semibold text-gray-900">€{{ number_format($item['cost'], 2, ',', '.') }}</p>
                                </div>
                            </div>
                        @endforeach
                    </div>

                    <!-- Total -->
                    <div class="mt-4 pt-4 border-t border-gray-200">
                        <div class="flex justify-between items-center">
                            <span class="text-xl font-semibold text-gray-900">Totale:</span>
                            <span class="text-2xl font-bold text-blue-600">€{{ number_format($preventivo->total_amount, 2, ',', '.') }}</span>
                        </div>
                    </div>
                </div>

                <!-- ChatGPT Analysis -->
                @if($preventivo->chatgpt_response)
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">
                            <i class="fas fa-robot mr-2 text-blue-600"></i>
                            Analisi ChatGPT
                        </h3>
                        <div class="prose max-w-none">
                            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                <p class="text-gray-700 whitespace-pre-line">{{ $preventivo->chatgpt_response }}</p>
                            </div>
                        </div>
                    </div>
                @endif
            </div>

            <!-- Right Column - Status and Actions -->
            <div class="space-y-6">
                <!-- Status Card -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Stato Preventivo</h3>
                    
                    <div class="space-y-4">
                        <div>
                            <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full {{ $preventivo->status_color }}">
                                {{ $preventivo->status_label }}
                            </span>
                        </div>

                        <!-- Status Change Form -->
                        <form action="{{ route('preventivi.update', $preventivo) }}" method="POST">
                            @csrf
                            @method('PATCH')
                            
                            <!-- Hidden fields to maintain current data -->
                            <input type="hidden" name="client_id" value="{{ $preventivo->client_id }}">
                            <input type="hidden" name="project_id" value="{{ $preventivo->project_id }}">
                            <input type="hidden" name="job_description" value="{{ $preventivo->job_description }}">
                            @foreach($preventivo->work_items as $index => $item)
                                <input type="hidden" name="work_items[{{ $index }}][description]" value="{{ $item['description'] }}">
                                <input type="hidden" name="work_items[{{ $index }}][cost]" value="{{ $item['cost'] }}">
                            @endforeach
                            
                            <div>
                                <label for="status" class="block text-sm font-medium text-gray-700 mb-2">
                                    Cambia Stato
                                </label>
                                <select name="status" 
                                        id="status"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                    @foreach(\App\Models\Preventivo::getStatuses() as $value => $label)
                                        <option value="{{ $value }}" {{ $preventivo->status === $value ? 'selected' : '' }}>
                                            {{ $label }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            
                            <button type="submit"
                                    class="w-full mt-3 px-4 py-2 text-white rounded-lg transition-colors duration-200"
                                    style="background-color: #007BCE;"
                                    onmouseover="this.style.backgroundColor='#005B99'"
                                    onmouseout="this.style.backgroundColor='#007BCE'">
                                Aggiorna Stato
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Quick Stats -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Statistiche</h3>
                    
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Numero voci:</span>
                            <span class="text-sm font-medium text-gray-900">{{ count($preventivo->work_items) }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Costo medio per voce:</span>
                            <span class="text-sm font-medium text-gray-900">
                                €{{ number_format($preventivo->total_amount / count($preventivo->work_items), 2, ',', '.') }}
                            </span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Ultima modifica:</span>
                            <span class="text-sm font-medium text-gray-900">{{ $preventivo->updated_at->format('d/m/Y H:i') }}</span>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Azioni</h3>
                    
                    <div class="space-y-3">
                        <a href="{{ route('preventivi.edit', $preventivo) }}"
                           class="w-full inline-flex justify-center items-center px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors duration-200">
                            <i class="fas fa-edit mr-2"></i>
                            Modifica Preventivo
                        </a>
                        
                        <form action="{{ route('preventivi.destroy', $preventivo) }}" 
                              method="POST" 
                              onsubmit="return confirm('Sei sicuro di voler eliminare questo preventivo?')">
                            @csrf
                            @method('DELETE')
                            <button type="submit"
                                    class="w-full inline-flex justify-center items-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors duration-200">
                                <i class="fas fa-trash mr-2"></i>
                                Elimina Preventivo
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
